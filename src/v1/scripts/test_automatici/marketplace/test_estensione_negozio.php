<?php

$base_path = dirname(__FILE__) . "/../../../";

require_once $base_path . "models/log_helper.php"; 	// Implementa la classe di gestione dei log
require_once $base_path . "models/jwt_helper.php"; 	// Implementa la classe di decodifica degli oggetti jwt
require_once $base_path . "models/class_db.php"; 	// Implementa la classe di connessione a postgres
require_once $base_path . "models/class_pdb.php"; 	// Implementa la classe di connessione a postgres
require_once $base_path . "models/class_couch.php"; // Implementa la classe di connessione a couchdb
require_once $base_path . "models/class_data.php"; 	// Implementa la classe Data generica di accesso ai dati mastercom
require_once $base_path . "models/class_auth.php";  // Implementa la classe Auth per l'autenticazione
require_once $base_path . "models/class_marketplace.php"; // Implementa la classe Marketplace

// Cambia la directory di lavoro per i percorsi relativi nelle classi
chdir($base_path);

// Acquisisci parametri dal terminale
$username = $argv[1];
$password = $argv[2];
$cleanup = $argv[3] ?? 'true'; // Parametro per eliminare i dati di test (default: true)
$db_richiesto = $argv[4] ?? ''; // Database opzionale

if (empty($username) || empty($password)) {
    die("Usage: php test_estensione_negozio.php <username> <password> [cleanup=true] [db_richiesto]\n" .
        "Esempio: php test_estensione_negozio.php admin password true\n" .
        "Questo script testa le nuove API dell'estensione negozio.\n");
}

$cleanup = ($cleanup === 'true' || $cleanup === '1');

// Inizializza gli oggetti necessari
$data = new Data();
$auth = new Auth($data);

// Effettua il login
$authorized = $auth->auth($username, $password);

if (!$authorized) {
    echo "Login failed: Invalid username or password\n";
    exit(1);
}

echo "Login successful.\n";
echo "=== TEST ESTENSIONE NEGOZIO ===\n";
echo "Cleanup abilitato: " . ($cleanup ? 'SI' : 'NO') . "\n";
if (!empty($db_richiesto)) {
    echo "Database richiesto: {$db_richiesto}\n";
}
echo "\n";

// Istanzia Marketplace
$marketplace = new Marketplace($auth);

// Array per tenere traccia degli ID creati per il cleanup
$created_items = [
    'marketplace' => [],
    'acquisti' => []
];

// Contatori per i test
$test_totali = 0;
$test_successi = 0;
$test_fallimenti = 0;

function esegui_test($nome_test, $callback, &$test_totali, &$test_successi, &$test_fallimenti) {
    $test_totali++;
    echo "\n--- TEST: {$nome_test} ---\n";

    try {
        $risultato = $callback();
        if ($risultato) {
            echo "✓ SUCCESSO: {$nome_test}\n";
            $test_successi++;
            return true;
        } else {
            echo "✗ FALLIMENTO: {$nome_test}\n";
            $test_fallimenti++;
            return false;
        }
    } catch (Exception $e) {
        echo "✗ ERRORE: {$nome_test} - " . $e->getMessage() . "\n";
        $test_fallimenti++;
        return false;
    }
}

// Trova uno studente per i test
$id_studente_test = null;
esegui_test("Ricerca studente per test", function() use ($data, &$id_studente_test) {
    $sql = "SELECT id_studente FROM studenti WHERE flag_canc = 0 LIMIT 1";
    $query = $data->db->prepare($sql);
    $query->execute();
    $studenti = $query->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($studenti)) {
        $id_studente_test = $studenti[0]['id_studente'];
        echo "  Studente trovato per test: ID {$id_studente_test}\n";
        return true;
    } else {
        echo "  Nessuno studente trovato nel database\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 1: Creazione articoli negozio per i test
esegui_test("Creazione articoli negozio per test", function() use ($marketplace, $db_richiesto, &$created_items) {
    $articoli_test = [
        [
            'descrizione' => 'Maglietta Test Negozio',
            'tipo' => 'ABBIGLIAMENTO',
            'categoria' => 'NEGOZIO',
            'ordinamento' => '001',
            'nome_sitoapp' => 'Maglietta Test',
            'descrizione_sitoapp' => 'Maglietta di test per negozio online',
            'pubblica_sitoapp' => 'SI',
            'validita_inizio' => time(),
            'validita_fine' => time() + (365 * 24 * 60 * 60),
            'caratteristiche' => [
                'oggetto_negozio' => [
                    'valuta' => 'euro',
                    'prezzo_unitario' => '25.00',
                    'prezzo_scontato' => '20.00',
                    'sconto_valido_dal' => date('d/m/Y'),
                    'sconto_valido_al' => date('d/m/Y', time() + (30 * 24 * 60 * 60)),
                    'tipo_pagamento_disponibile' => 'online',
                    'consenti_ordine_multiplo' => 'SI',
                    'oggetto_disponibile_per' => 'tutti'
                ]
            ]
        ],
        [
            'descrizione' => 'Libro Test Negozio',
            'tipo' => 'LIBRI',
            'categoria' => 'NEGOZIO',
            'ordinamento' => '002',
            'nome_sitoapp' => 'Libro Test',
            'descrizione_sitoapp' => 'Libro di test per negozio online',
            'pubblica_sitoapp' => 'SI',
            'validita_inizio' => time(),
            'validita_fine' => time() + (365 * 24 * 60 * 60),
            'caratteristiche' => [
                'oggetto_negozio' => [
                    'valuta' => 'euro',
                    'prezzo_unitario' => '15.50',
                    'tipo_pagamento_disponibile' => 'predefinito',
                    'consenti_ordine_multiplo' => 'NO',
                    'oggetto_disponibile_per' => 'tutti'
                ]
            ]
        ]
    ];

    $creati = 0;
    foreach ($articoli_test as $articolo) {
        if (!empty($db_richiesto)) {
            $articolo['db_richiesto'] = $db_richiesto;
        }

        $risultato = $marketplace->inserisciMarketplaceItem($articolo);

        if (is_numeric($risultato) && $risultato > 0) {
            $created_items['marketplace'][] = $risultato;
            $creati++;
            echo "  Articolo creato: ID {$risultato} - {$articolo['descrizione']}\n";
        }
    }

    return $creati === count($articoli_test);
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 2: Estrazione lista oggetti negozio per studente
esegui_test("Estrazione lista oggetti negozio per studente", function() use ($marketplace, $db_richiesto, $created_items, $id_studente_test) {
    if (!$id_studente_test || empty($created_items['marketplace'])) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $filter = ['id_studente' => $id_studente_test];
    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->estraiListaOggettiNegozio($filter);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        $oggetti = $risultato['oggetti_negozio'];
        echo "  Oggetti negozio trovati: " . count($oggetti) . "\n";
        echo "  NOTA: Vengono estratti solo oggetti con pubblica_sitoapp = 'SI'\n";

        // Verifica che almeno alcuni degli articoli creati siano presenti
        $trovati = 0;
        foreach ($created_items['marketplace'] as $id_creato) {
            if (isset($oggetti[$id_creato])) {
                $trovati++;
                echo "  ✓ Articolo {$id_creato} disponibile: " . $oggetti[$id_creato]['descrizione'] . "\n";
            }
        }

        return $trovati > 0;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 3: Inserimento articolo nel carrello
esegui_test("Inserimento articolo nel carrello", function() use ($marketplace, $db_richiesto, &$created_items, $id_studente_test) {
    if (!$id_studente_test || empty($created_items['marketplace'])) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $id_marketplace = $created_items['marketplace'][0];
    $dati = [
        'id_studente' => $id_studente_test,
        'id_marketplace' => $id_marketplace,
        'opzioni' => [
            'taglia' => 'M',
            'colore' => 'Blu',
            'note' => 'Test carrello'
        ]
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->inserisciArticoloCarrello($dati);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        $created_items['acquisti'][] = $risultato['id_acquisto'];
        echo "  Articolo aggiunto al carrello: ID {$risultato['id_acquisto']}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 4: Inserimento secondo articolo nel carrello
esegui_test("Inserimento secondo articolo nel carrello", function() use ($marketplace, $db_richiesto, &$created_items, $id_studente_test) {
    if (!$id_studente_test || count($created_items['marketplace']) < 2) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $id_marketplace = $created_items['marketplace'][1];
    $dati = [
        'id_studente' => $id_studente_test,
        'id_marketplace' => $id_marketplace,
        'opzioni' => [
            'personalizzazione' => 'Nome studente',
            'note' => 'Test secondo articolo carrello'
        ]
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->inserisciArticoloCarrello($dati);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        $created_items['acquisti'][] = $risultato['id_acquisto'];
        echo "  Secondo articolo aggiunto al carrello: ID {$risultato['id_acquisto']}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 5: Estrazione articoli carrello
esegui_test("Estrazione articoli carrello", function() use ($marketplace, $db_richiesto, $created_items, $id_studente_test) {
    if (!$id_studente_test) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $filter = ['id_studente' => $id_studente_test];
    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->estraiArticoliCarrello($filter);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        $carrello = $risultato['carrello'];
        echo "  Articoli nel carrello: " . $risultato['totale_articoli'] . "\n";

        foreach ($carrello as $id_acquisto => $articolo) {
            echo "  - Articolo {$id_acquisto}: {$articolo['descrizione']} (stato: {$articolo['stato_ordine']})\n";
        }

        return $risultato['totale_articoli'] > 0;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 6: Verifica disponibilità articoli carrello
esegui_test("Verifica disponibilità articoli carrello", function() use ($marketplace, $db_richiesto, $id_studente_test) {
    if (!$id_studente_test) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $filter = ['id_studente' => $id_studente_test];
    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->verificaDisponibilitaArticoliCarrello($filter);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        echo "  Articoli disponibili: " . $risultato['totale_articoli_disponibili'] . "\n";
        echo "  Articoli non disponibili: " . $risultato['totale_articoli_non_disponibili'] . "\n";
        echo "  Totale importo: €" . number_format($risultato['totale_importo'], 2) . "\n";
        echo "  Carrello valido: " . ($risultato['carrello_valido'] ? 'SI' : 'NO') . "\n";

        if (!empty($risultato['articoli_non_disponibili'])) {
            foreach ($risultato['articoli_non_disponibili'] as $id_acquisto => $articolo) {
                echo "  ⚠️  Articolo non disponibile {$id_acquisto}: {$articolo['motivo_indisponibilita']}\n";
            }
        }

        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 7: Test inserimento duplicato nel carrello (deve fallire)
esegui_test("Test inserimento duplicato nel carrello", function() use ($marketplace, $db_richiesto, $created_items, $id_studente_test) {
    if (!$id_studente_test || empty($created_items['marketplace'])) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    // Prova a inserire di nuovo il primo articolo
    $id_marketplace = $created_items['marketplace'][0];
    $dati = [
        'id_studente' => $id_studente_test,
        'id_marketplace' => $id_marketplace,
        'opzioni' => ['note' => 'Test duplicato']
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->inserisciArticoloCarrello($dati);

    // Deve fallire perché l'articolo è già nel carrello
    if (is_array($risultato) && $risultato['esito'] === 'ERRORE') {
        echo "  ✓ Duplicato correttamente rifiutato: " . json_encode($risultato['errori']) . "\n";
        return true;
    } else {
        echo "  ✗ Il duplicato non è stato rifiutato: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 8: Eliminazione articolo dal carrello
esegui_test("Eliminazione articolo dal carrello", function() use ($marketplace, $db_richiesto, &$created_items, $id_studente_test) {
    if (!$id_studente_test || empty($created_items['acquisti'])) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $id_acquisto_da_eliminare = $created_items['acquisti'][0];
    $dati = [
        'id_acquisto' => $id_acquisto_da_eliminare,
        'id_studente' => $id_studente_test
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->eliminaArticoloCarrello($dati);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        echo "  Articolo eliminato dal carrello: ID {$id_acquisto_da_eliminare}\n";
        // Rimuovi dall'array per il cleanup
        $key = array_search($id_acquisto_da_eliminare, $created_items['acquisti']);
        if ($key !== false) {
            unset($created_items['acquisti'][$key]);
        }
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 9: Verifica carrello dopo eliminazione
esegui_test("Verifica carrello dopo eliminazione", function() use ($marketplace, $db_richiesto, $id_studente_test) {
    if (!$id_studente_test) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $filter = ['id_studente' => $id_studente_test];
    if (!empty($db_richiesto)) {
        $filter['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->estraiArticoliCarrello($filter);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        echo "  Articoli rimasti nel carrello: " . $risultato['totale_articoli'] . "\n";

        // Dovrebbe essere rimasto solo 1 articolo
        return $risultato['totale_articoli'] === 1;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 10: Test aggiornamento tipo movimento
esegui_test("Test aggiornamento tipo movimento", function() use ($marketplace, $db_richiesto, $created_items) {
    if (empty($created_items['marketplace'])) {
        echo "  Prerequisiti mancanti\n";
        return false;
    }

    $id_marketplace = $created_items['marketplace'][0];
    $dati = [
        'id_marketplace' => $id_marketplace,
        'id_tipo_movimento' => 123 // Valore di test
    ];

    if (!empty($db_richiesto)) {
        $dati['db_richiesto'] = $db_richiesto;
    }

    $risultato = $marketplace->updateTipoMovimentoMarketplace($dati);

    if (is_array($risultato) && $risultato['esito'] === 'OK') {
        echo "  Tipo movimento aggiornato per marketplace {$id_marketplace}\n";
        return true;
    } else {
        echo "  Errore: " . json_encode($risultato) . "\n";
        return false;
    }
}, $test_totali, $test_successi, $test_fallimenti);

// TEST 11: Test gestione errori
esegui_test("Test gestione errori", function() use ($marketplace, $db_richiesto) {
    $test_errori = [
        // Test estrazione oggetti negozio senza id_studente
        [
            'funzione' => 'estraiListaOggettiNegozio',
            'dati' => [],
            'descrizione' => 'Estrazione oggetti negozio senza id_studente'
        ],
        // Test inserimento carrello con id_marketplace inesistente
        [
            'funzione' => 'inserisciArticoloCarrello',
            'dati' => ['id_studente' => 1, 'id_marketplace' => 999999],
            'descrizione' => 'Inserimento carrello con id_marketplace inesistente'
        ],
        // Test eliminazione carrello con id_acquisto inesistente
        [
            'funzione' => 'eliminaArticoloCarrello',
            'dati' => ['id_acquisto' => 999999, 'id_studente' => 1],
            'descrizione' => 'Eliminazione carrello con id_acquisto inesistente'
        ]
    ];

    $errori_gestiti = 0;
    foreach ($test_errori as $test) {
        if (!empty($db_richiesto)) {
            $test['dati']['db_richiesto'] = $db_richiesto;
        }

        $risultato = $marketplace->{$test['funzione']}($test['dati']);

        if (is_array($risultato) && $risultato['esito'] === 'ERRORE') {
            echo "  ✓ Errore gestito: " . $test['descrizione'] . "\n";
            $errori_gestiti++;
        } else {
            echo "  ✗ Errore NON gestito: " . $test['descrizione'] . "\n";
        }
    }

    return $errori_gestiti === count($test_errori);
}, $test_totali, $test_successi, $test_fallimenti);

// CLEANUP: Eliminazione dati di test se richiesto
if ($cleanup) {
    echo "\n=== CLEANUP DATI DI TEST ===\n";

    // Elimina acquisti rimasti
    if (!empty($created_items['acquisti'])) {
        esegui_test("Cleanup acquisti", function() use ($marketplace, $db_richiesto, $created_items) {
            $eliminati = 0;
            foreach ($created_items['acquisti'] as $id_acquisto) {
                $dati = ['id_acquisto' => $id_acquisto];
                if (!empty($db_richiesto)) {
                    $dati['db_richiesto'] = $db_richiesto;
                }

                $risultato = $marketplace->softDeleteItemAcquisti($dati);

                if (is_array($risultato) && $risultato['esito'] === 'OK') {
                    echo "  ✓ Acquisto {$id_acquisto} eliminato\n";
                    $eliminati++;
                } else {
                    echo "  ✗ Errore eliminando acquisto {$id_acquisto}: " . json_encode($risultato) . "\n";
                }
            }

            return $eliminati === count($created_items['acquisti']);
        }, $test_totali, $test_successi, $test_fallimenti);
    }

    // Elimina elementi marketplace
    if (!empty($created_items['marketplace'])) {
        esegui_test("Cleanup marketplace", function() use ($marketplace, $db_richiesto, $created_items) {
            $eliminati = 0;
            foreach ($created_items['marketplace'] as $id_marketplace) {
                $dati = ['id_marketplace' => $id_marketplace];
                if (!empty($db_richiesto)) {
                    $dati['db_richiesto'] = $db_richiesto;
                }

                $risultato = $marketplace->softDeleteItemMarketplace($dati);

                if (is_array($risultato) && $risultato['esito'] === 'OK') {
                    echo "  ✓ Marketplace {$id_marketplace} eliminato\n";
                    $eliminati++;
                } else {
                    echo "  ✗ Errore eliminando marketplace {$id_marketplace}: " . json_encode($risultato) . "\n";
                }
            }

            return $eliminati === count($created_items['marketplace']);
        }, $test_totali, $test_successi, $test_fallimenti);
    }
} else {
    echo "\n=== CLEANUP DISABILITATO ===\n";
    echo "Dati di test mantenuti nel database:\n";
    echo "- Marketplace creati: " . count($created_items['marketplace']) . " (IDs: " . implode(', ', $created_items['marketplace']) . ")\n";
    echo "- Acquisti creati: " . count($created_items['acquisti']) . " (IDs: " . implode(', ', $created_items['acquisti']) . ")\n";
}

// RIEPILOGO FINALE
echo "\n=== RIEPILOGO FINALE ===\n";
echo "Test totali eseguiti: {$test_totali}\n";
echo "Test riusciti: {$test_successi}\n";
echo "Test falliti: {$test_fallimenti}\n";

$percentuale_successo = $test_totali > 0 ? round(($test_successi / $test_totali) * 100, 2) : 0;
echo "Percentuale successo: {$percentuale_successo}%\n";

if ($test_fallimenti === 0) {
    echo "\n🎉 TUTTI I TEST SONO PASSATI! 🎉\n";
    echo "Le nuove API dell'estensione negozio funzionano correttamente.\n";
} else {
    echo "\n⚠️  ALCUNI TEST SONO FALLITI ⚠️\n";
    echo "Verificare i messaggi di errore sopra riportati.\n";
}

// Dettagli sui test eseguiti
echo "\n=== DETTAGLI TEST ESEGUITI ===\n";
echo "1. Ricerca studente per test\n";
echo "2. Creazione articoli negozio per test\n";
echo "3. Estrazione lista oggetti negozio per studente\n";
echo "4. Inserimento articolo nel carrello\n";
echo "5. Inserimento secondo articolo nel carrello\n";
echo "6. Estrazione articoli carrello\n";
echo "7. Verifica disponibilità articoli carrello\n";
echo "8. Test inserimento duplicato nel carrello\n";
echo "9. Eliminazione articolo dal carrello\n";
echo "10. Verifica carrello dopo eliminazione\n";
echo "11. Test aggiornamento tipo movimento\n";
echo "12. Test gestione errori\n";

if ($cleanup) {
    echo "13. Cleanup acquisti\n";
    echo "14. Cleanup marketplace\n";
}

echo "\n=== NUOVE API TESTATE ===\n";
echo "✓ estraiListaOggettiNegozio() - Estrazione oggetti negozio per studente\n";
echo "✓ inserisciArticoloCarrello() - Inserimento articoli nel carrello\n";
echo "✓ eliminaArticoloCarrello() - Eliminazione articoli dal carrello\n";
echo "✓ estraiArticoliCarrello() - Estrazione contenuto carrello\n";
echo "✓ verificaDisponibilitaArticoliCarrello() - Verifica disponibilità pre-checkout\n";
echo "✓ updateTipoMovimentoMarketplace() - Aggiornamento tipi movimento\n";

echo "\n=== ENDPOINT CONTROLLER AGGIUNTI ===\n";
echo "✓ GET /marketplace/lista_oggetti_negozio_studente\n";
echo "✓ GET /marketplace/carrello\n";
echo "✓ GET /marketplace/verifica_carrello\n";
echo "✓ POST /marketplace/carrello/inserisci\n";
echo "✓ PUT /marketplace/carrello/elimina\n";
echo "✓ PUT /marketplace/aggiorna_tipo_movimento\n";

echo "\n=== TEST COMPLETATO ===\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";

// Exit code basato sui risultati
exit($test_fallimenti > 0 ? 1 : 0);

?>
