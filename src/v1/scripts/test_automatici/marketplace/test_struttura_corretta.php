<?php

$base_path = dirname(__FILE__) . "/../../../";

require_once $base_path . "models/log_helper.php";
require_once $base_path . "models/jwt_helper.php";
require_once $base_path . "models/class_db.php";
require_once $base_path . "models/class_pdb.php";
require_once $base_path . "models/class_couch.php";
require_once $base_path . "models/class_data.php";
require_once $base_path . "models/class_auth.php";
require_once $base_path . "models/class_marketplace.php";

// Cambia la directory di lavoro per i percorsi relativi nelle classi
chdir($base_path);

// Acquisisci parametri dal terminale
$username = $argv[1];
$password = $argv[2];
$db_richiesto = $argv[3] ?? '';

if (empty($username) || empty($password)) {
    die("Usage: php test_struttura_corretta.php <username> <password> [db_richiesto]\n" .
        "Questo script verifica che la struttura delle tabelle sia corretta.\n");
}

// Inizializza gli oggetti necessari
$data = new Data();
$auth = new Auth($data);

// Effettua il login
$authorized = $auth->auth($username, $password);

if (!$authorized) {
    echo "Login failed: Invalid username or password\n";
    exit(1);
}

echo "Login successful.\n";
echo "=== VERIFICA STRUTTURA TABELLE MARKETPLACE ===\n";

if (!empty($db_richiesto)) {
    echo "Database richiesto: {$db_richiesto}\n";
    $data->SetDb($db_richiesto);
}

// Istanzia Marketplace
$marketplace = new Marketplace($auth);

echo "\n--- VERIFICA CAMPI TABELLA MARKETPLACE ---\n";

// Verifica struttura tabella marketplace
$sql_marketplace = "SELECT column_name, data_type, is_nullable, column_default 
                    FROM information_schema.columns 
                    WHERE table_name = 'marketplace' 
                    ORDER BY ordinal_position";

$query = $data->db->prepare($sql_marketplace);
$query->execute();
$campi_marketplace = $query->fetchAll(PDO::FETCH_ASSOC);

$campi_richiesti_marketplace = [
    'id_marketplace', 'descrizione', 'tipo', 'categoria', 'caratteristiche',
    'nome_sitoapp', 'descrizione_sitoapp', 'pubblica_sitoapp', 'ordinamento',
    'validita_inizio', 'validita_fine', 'id_tipo_movimento'
];

echo "Campi trovati nella tabella marketplace:\n";
$campi_trovati_marketplace = [];
foreach ($campi_marketplace as $campo) {
    $campi_trovati_marketplace[] = $campo['column_name'];
    echo "  - {$campo['column_name']} ({$campo['data_type']})\n";
}

echo "\nVerifica campi richiesti:\n";
foreach ($campi_richiesti_marketplace as $campo_richiesto) {
    if (in_array($campo_richiesto, $campi_trovati_marketplace)) {
        echo "  ✓ {$campo_richiesto} - PRESENTE\n";
    } else {
        echo "  ✗ {$campo_richiesto} - MANCANTE\n";
    }
}

echo "\n--- VERIFICA CAMPI TABELLA MARKETPLACE_STUDENTI_ACQUISTI ---\n";

// Verifica struttura tabella marketplace_studenti_acquisti
$sql_acquisti = "SELECT column_name, data_type, is_nullable, column_default 
                 FROM information_schema.columns 
                 WHERE table_name = 'marketplace_studenti_acquisti' 
                 ORDER BY ordinal_position";

$query = $data->db->prepare($sql_acquisti);
$query->execute();
$campi_acquisti = $query->fetchAll(PDO::FETCH_ASSOC);

$campi_richiesti_acquisti = [
    'id_acquisto', 'id_studente', 'id_marketplace', 'contabilizzato',
    'stato_ordine', 'opzioni', 'validita_inizio', 'validita_fine'
];

echo "Campi trovati nella tabella marketplace_studenti_acquisti:\n";
$campi_trovati_acquisti = [];
foreach ($campi_acquisti as $campo) {
    $campi_trovati_acquisti[] = $campo['column_name'];
    echo "  - {$campo['column_name']} ({$campo['data_type']})\n";
}

echo "\nVerifica campi richiesti:\n";
foreach ($campi_richiesti_acquisti as $campo_richiesto) {
    if (in_array($campo_richiesto, $campi_trovati_acquisti)) {
        echo "  ✓ {$campo_richiesto} - PRESENTE\n";
    } else {
        echo "  ✗ {$campo_richiesto} - MANCANTE\n";
    }
}

echo "\n--- TEST FUNZIONALITÀ BASE ---\n";

// Test estrazione oggetti negozio (deve funzionare anche se non ci sono oggetti)
$id_studente_test = 1; // ID di test
$filter = ['id_studente' => $id_studente_test];
if (!empty($db_richiesto)) {
    $filter['db_richiesto'] = $db_richiesto;
}

echo "Test estrazione oggetti negozio...\n";
$risultato = $marketplace->estraiListaOggettiNegozio($filter);

if (is_array($risultato) && $risultato['esito'] === 'OK') {
    echo "  ✓ Funzione estraiListaOggettiNegozio() funziona correttamente\n";
    echo "  - Oggetti trovati: " . count($risultato['oggetti_negozio']) . "\n";
} else {
    echo "  ✗ Errore nella funzione estraiListaOggettiNegozio(): " . json_encode($risultato) . "\n";
}

// Test estrazione carrello (deve funzionare anche se vuoto)
echo "\nTest estrazione carrello...\n";
$risultato_carrello = $marketplace->estraiArticoliCarrello($filter);

if (is_array($risultato_carrello) && $risultato_carrello['esito'] === 'OK') {
    echo "  ✓ Funzione estraiArticoliCarrello() funziona correttamente\n";
    echo "  - Articoli nel carrello: " . $risultato_carrello['totale_articoli'] . "\n";
} else {
    echo "  ✗ Errore nella funzione estraiArticoliCarrello(): " . json_encode($risultato_carrello) . "\n";
}

echo "\n--- VERIFICA CONSTRAINT STATO_ORDINE ---\n";

// Verifica constraint su stato_ordine
$sql_constraint = "SELECT conname, consrc 
                   FROM pg_constraint 
                   WHERE conrelid = 'marketplace_studenti_acquisti'::regclass 
                     AND contype = 'c'
                     AND consrc LIKE '%stato_ordine%'";

$query = $data->db->prepare($sql_constraint);
$query->execute();
$constraints = $query->fetchAll(PDO::FETCH_ASSOC);

if (!empty($constraints)) {
    echo "Constraint trovati per stato_ordine:\n";
    foreach ($constraints as $constraint) {
        echo "  ✓ {$constraint['conname']}: {$constraint['consrc']}\n";
    }
} else {
    echo "  ⚠️  Nessun constraint trovato per stato_ordine\n";
    echo "  Eseguire: ALTER TABLE marketplace_studenti_acquisti ADD CONSTRAINT check_stato_ordine CHECK (stato_ordine IN ('', 'CARRELLO', 'ORDINATO', 'CONFERMATO', 'EVASO', 'ANNULLATO'));\n";
}

echo "\n=== RIEPILOGO ===\n";
echo "Struttura tabelle verificata.\n";
echo "Se ci sono campi mancanti, eseguire le query in src/v1/configs/marketplace.sql\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";

?>
